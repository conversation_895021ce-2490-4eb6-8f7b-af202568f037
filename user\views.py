from django.shortcuts import render, redirect
from django.contrib.auth.hashers import make_password, check_password
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import Group
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.db import connection
from django.db.models import Q
from django.utils import timezone
from datetime import datetime

from user.models import User, AdminActionLog, UserActionLog
from core.decorators import module_permission_required

# Create your views here.
@login_required
@module_permission_required(module='user', required_level='view')
def index(request):
    # Get all users except those with admin role for the employees list
    employees = User.objects.exclude(role='admin')

    # Get system users (employees with system access)
    system_users = User.objects.filter(is_staff=True).exclude(role='admin')

    # Get admin users for the admin tab
    admin_users = User.objects.filter(role='admin')

    context = {
        'employees': employees,
        'system_users': system_users,
        'admin_users': admin_users,
    }
    return render(request, 'user/index.html', context)

@login_required
@module_permission_required(module='user', required_level='edit')
def add_employee(request):
    """
    Dedicated page for adding a new employee
    """
    if request.method == "POST":
        # Get form data
        name = request.POST.get("name")
        phone = request.POST.get("phone")

        # Parse date fields from datepicker format (mm/dd/yyyy) to Django format (yyyy-mm-dd)
        dob_str = request.POST.get("dob")
        join_date_str = request.POST.get("join_date")

        # Convert date strings to proper format
        try:
            if dob_str:
                dob = datetime.strptime(dob_str, '%m/%d/%Y').strftime('%Y-%m-%d')
            else:
                dob = None

            if join_date_str:
                join_date = datetime.strptime(join_date_str, '%m/%d/%Y').strftime('%Y-%m-%d')
            else:
                join_date = None
        except ValueError:
            # If date parsing fails, use the original strings (they might already be in the correct format)
            dob = dob_str
            join_date = join_date_str

        gender = request.POST.get("gender")
        role = request.POST.get("role")
        salary = request.POST.get("salary")
        address = request.POST.get("address")
        work_schedule = request.POST.get("schedule")
        email = request.POST.get("email", "")  # Optional email field

        # Validate form data
        if User.objects.filter(phone=phone).exists():
            messages.error(request, f"Phone number '{phone}' is already registered. Please use a different phone number.")
            return redirect('user:add_employee')

        try:
            # Get the last user ID
            last_user = User.objects.last()
            if last_user:
                lastId = last_user.id
            else:
                lastId = 0

            # Find the highest employee ID number to ensure uniqueness
            highest_emp_id = 0
            for emp in User.objects.filter(emp_id__isnull=False):
                if emp.emp_id and emp.emp_id.startswith('LFC-'):
                    try:
                        id_num = int(emp.emp_id.split('-')[1])
                        highest_emp_id = max(highest_emp_id, id_num)
                    except (ValueError, IndexError):
                        pass

            # Generate the next employee ID
            next_emp_id = highest_emp_id + 1

            # Create the employee (without system access)
            user = User.objects.create(
                username=f"emp_{lastId+1}",  # Generate a temporary username
                name=name,
                phone=phone,
                dob=dob,
                gender=gender,
                salary=salary,
                # Store both address and work schedule in the address field
                address=f"Address: {address}\n\nWork Schedule: {work_schedule}",
                password=make_password("defaultpassword"),  # Set a default password
                emp_id=f"LFC-{next_emp_id:03d}",  # Format: LFC-001, LFC-002, etc.
                join_date=join_date,  # Use the join date from the form
                is_manager=(role == 'admin'),  # Only admin users are managers now
                is_employee=True,
                is_staff=False,  # No system access by default
                email=email,  # Use the email from the form
                role=role  # Store the role for later use in user registration
            )

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                photo = request.FILES['photo']
                if photo:
                    # Save the photo in media/employee_photos directory
                    path = f'employee_photos/{user.id}_{photo.name}'
                    default_storage.save(path, ContentFile(photo.read()))

                    # Store the photo path directly in the user object
                    user.photo = path
                    user.save()

            messages.success(request, f"Employee '{name}' created successfully. To give system access, use the User Registration form.")
            return redirect('user:index')  # Redirect to employee list after successful creation

        except Exception as e:
            messages.error(request, f"Error creating employee: {str(e)}")

    return render(request, 'user/add_employee.html')


@login_required
@module_permission_required(module='user', required_level='edit')
def user_registration(request):
    # Get employees without system access (excluding admin users)
    employees_without_access = User.objects.filter(is_staff=False).exclude(role='admin')

    # Get users with system access (excluding admin users)
    users_with_access = User.objects.filter(is_staff=True).exclude(role='admin')

    if request.method == "POST":
        # Get form data
        employee_id = request.POST.get("employee")
        username = request.POST.get("username")
        email = request.POST.get("email")
        role = request.POST.get("role")
        password = request.POST.get("password")
        confirm_password = request.POST.get("confirm_password")

        # Validate form data
        if User.objects.filter(username=username).exists():
            messages.error(request, f"Username '{username}' already exists. Please choose a different username.")
            return redirect('user:register')

        if password != confirm_password:
            messages.error(request, "Passwords do not match.")
            return redirect('user:register')

        try:
            # Get the employee
            employee = User.objects.get(id=employee_id)

            # Handle role assignment based on employee's current role
            if employee.role:
                # For roles that can have system access, enforce exact match
                if employee.role in ['cashier', 'coach']:
                    if employee.role != role:
                        messages.warning(request, f"Role must match the employee's role. Automatically setting role to {employee.role}.")
                        role = employee.role
                # For roles that can't have system access (cleaner, security), validate the selected role
                elif employee.role not in ['admin', 'cashier', 'coach']:
                    # Validate that the selected role can have system access
                    if role not in ['cashier', 'coach']:
                        messages.error(request, f"Role '{role}' cannot have system access. Please select a system access role (Cashier or Coach).")
                        return redirect("user:register")
                    else:
                        # Allow changing from a non-system access role to a system access role
                        messages.success(request, f"Employee role will be changed from '{employee.role}' to '{role}' to grant system access.")
            else:
                # If employee has no role, validate that the selected role can have system access
                if role not in ['cashier', 'coach']:
                    messages.error(request, f"Role '{role}' cannot have system access. Please select a system access role (Cashier or Coach).")
                    return redirect("user:register")

            # Prevent non-admin users from being registered as admin
            if role == 'admin':
                messages.error(request, "Admin users cannot be created through this form. Please use the command line.")
                return redirect('user:register')

            # Determine if this role should have system access
            has_system_access = role in ['admin', 'cashier', 'coach']

            # Update the employee with user credentials
            employee.username = username
            employee.email = email
            employee.password = make_password(password)
            employee.is_manager = (role == 'admin')  # Only admin users are managers now
            employee.is_staff = has_system_access  # Only give system access to roles that need it
            employee.role = role  # Update the role
            employee.save()

            # Assign to appropriate group based on role
            # Clear existing groups first
            employee.groups.clear()

            # Add to appropriate group
            if role == 'admin':
                admin_group, _ = Group.objects.get_or_create(name='admin')
                employee.groups.add(admin_group)
            elif role == 'cashier':
                cashier_group, _ = Group.objects.get_or_create(name='cashier')
                employee.groups.add(cashier_group)
            elif role == 'coach':
                coach_group, _ = Group.objects.get_or_create(name='coach')
                employee.groups.add(coach_group)

            messages.success(request, f"User account created successfully for employee '{employee.name}'.")
            return redirect('/user/?tab=system-users')

        except User.DoesNotExist:
            messages.error(request, "Employee not found.")
        except Exception as e:
            messages.error(request, f"Error creating user account: {str(e)}")

    context = {
        'employees_without_access': employees_without_access,
        'users_with_access': users_with_access,
    }
    return render(request, 'user/user_registration.html', context)

@login_required
@module_permission_required(module='user', required_level='edit')
def edit(request, pk):

    try:
        employee = User.objects.get(id = pk)
    except User.DoesNotExist:
        messages.error(request, "Employee not found")
        return redirect("/user/")

    # Schedule and salary payment functionality removed

    # Handle regular form submission for employee details
    if request.method == "POST" and 'name' in request.POST:
        try:
            name = request.POST.get("name")
            phone = request.POST.get("phone")
            salary = request.POST.get("salary")
            join_date = request.POST.get("join_date")
            address = request.POST.get("address")
            work_schedule = request.POST.get("schedule")
            role = request.POST.get("role")
            password = request.POST.get("password")
            email = request.POST.get("email")

            # Store the old role for comparison
            old_role = employee.role

            employee.name = name
            employee.phone = phone
            employee.salary = salary
            employee.join_date = join_date
            # Combine address and work schedule
            employee.address = f"Address: {address}\n\nWork Schedule: {work_schedule}"
            employee.role = role

            # Update email if provided
            if email:
                employee.email = email

            # Update is_manager flag based on role
            employee.is_manager = (role == 'admin')

            # Determine if this role should have system access
            has_system_access = role in ['admin', 'cashier', 'coach']

            # Update is_staff flag if the employee has system access
            if employee.is_staff:
                employee.is_staff = has_system_access

            if password != "":
                employee.password = make_password(password)

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                photo = request.FILES['photo']
                if photo:
                    # Save the photo in media/employee_photos directory
                    path = f'employee_photos/{employee.id}_{photo.name}'
                    default_storage.save(path, ContentFile(photo.read()))

                    # Store the photo path directly in the employee object
                    employee.photo = path

            employee.save()

            # Update group assignments if the role has changed and the employee has system access
            if old_role != role and employee.is_staff:
                # Clear existing groups
                employee.groups.clear()

                # Add to appropriate group based on new role
                if role == 'admin':
                    admin_group, _ = Group.objects.get_or_create(name='admin')
                    employee.groups.add(admin_group)
                elif role == 'cashier':
                    cashier_group, _ = Group.objects.get_or_create(name='cashier')
                    employee.groups.add(cashier_group)
                elif role == 'coach':
                    coach_group, _ = Group.objects.get_or_create(name='coach')
                    employee.groups.add(coach_group)

                messages.success(request, f"Employee '{name}' updated successfully. Role changed from '{old_role}' to '{role}'.")
            else:
                messages.success(request, f"Employee '{name}' updated successfully.")
        except Exception as e:
            messages.error(request, f"Error updating employee: {str(e)}")

    context = {
        'employee': employee,
        'from_employee_list': True  # Flag to indicate we're editing from the Employees List
    }
    return render(request,"user/edit_empoloyee.html",context)



@login_required
@module_permission_required(module='user', required_level='edit')
def deactivate(request,pk):
    try:
        employee = User.objects.get(id = pk)

        # Protected usernames that cannot be deactivated
        PROTECTED_USERNAMES = ['developer', 'owner']

        # Check if the username is protected
        if employee.username in PROTECTED_USERNAMES:
            messages.error(request, f"User '{employee.name}' (username: {employee.username}) cannot be deactivated as it is a protected system account.")

            # Log the failed attempt
            AdminActionLog.log_action(
                user=request.user,
                action_type='deactivate_user',
                ip_address=get_client_ip(request),
                target_user=employee,
                description=f"Failed attempt to deactivate protected user {employee.username}"
            )

            return redirect("/user/")

        employee.is_active = False
        employee.save()

        # Log the successful deactivation
        AdminActionLog.log_action(
            user=request.user,
            action_type='deactivate_user',
            ip_address=get_client_ip(request),
            target_user=employee,
            description=f"Deactivated user {employee.username}"
        )

        messages.success(request, f"Employee '{employee.name}' deactivated successfully.")
    except Exception as e:
        messages.error(request, f"Error deactivating employee: {str(e)}")

    return redirect("/user/")

# Helper function to get client IP
def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

@login_required
@module_permission_required(module='user', required_level='edit')
def active(request,pk):
    try:
        employee = User.objects.get(id = pk)
        employee.is_active = True
        employee.save()

        # Log the activation
        AdminActionLog.log_action(
            user=request.user,
            action_type='activate_user',
            ip_address=get_client_ip(request),
            target_user=employee,
            description=f"Activated user {employee.username}"
        )

        messages.success(request, f"Employee '{employee.name}' activated successfully.")
    except Exception as e:
        messages.error(request, f"Error activating employee: {str(e)}")

    return redirect("/user/")

@login_required
@module_permission_required(module='user', required_level='full')
def delete(request, pk):
    try:
        employee = User.objects.get(id=pk)
        name = employee.name
        username = employee.username  # Store username before deletion

        # Protected usernames that cannot be deleted
        PROTECTED_USERNAMES = ['developer', 'owner']

        # Check if the username is protected
        if employee.username in PROTECTED_USERNAMES:
            messages.error(request, f"User '{name}' (username: {employee.username}) cannot be deleted as it is a protected system account.")

            # Log the failed attempt
            AdminActionLog.log_action(
                user=request.user,
                action_type='delete_user',
                ip_address=get_client_ip(request),
                target_user=employee,
                description=f"Failed attempt to delete protected user {employee.username}"
            )

            return redirect("/user/")

        # First, check if there's a corresponding record in the employee_employee table
        # and delete it if it exists
        with connection.cursor() as cursor:
            # Check if the record exists
            cursor.execute("SELECT id FROM employee_employee WHERE user_id = %s", [pk])
            employee_record = cursor.fetchone()

            if employee_record:
                # Delete the employee record first
                cursor.execute("DELETE FROM employee_employee WHERE user_id = %s", [pk])

        # Log the deletion before actually deleting the user
        AdminActionLog.log_action(
            user=request.user,
            action_type='delete_user',
            ip_address=get_client_ip(request),
            target_user=None,  # Can't reference the user after deletion
            description=f"Deleted user {username} (ID: {pk}, Name: {name})"
        )

        # Now delete the user
        employee.delete()
        messages.success(request, f"Employee '{name}' deleted successfully.")
    except Exception as e:
        messages.error(request, f"Error deleting employee: {str(e)}")

    return redirect("/user/")

# Admin users management - now redirects to index with admin tab
@login_required
@module_permission_required(module='user', required_level='view')
def admin_users(request):
    # Redirect to index page with a query parameter to activate the admin tab
    messages.info(request, "Viewing admin users")
    return redirect('/user/?tab=admins')

# Edit system user (simplified form for users with system access)
@login_required
@module_permission_required(module='user', required_level='edit')
def edit_system_user(request, pk):
    try:
        employee = User.objects.get(id=pk)
    except User.DoesNotExist:
        messages.error(request, "User not found")
        return redirect("user:register")

    # Handle form submission
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            role = request.POST.get("role")
            password = request.POST.get("password")
            email = request.POST.get("email")
            join_date = request.POST.get("join_date")

            # Validate that the role is one that should have system access
            if role not in ['admin', 'cashier', 'coach']:
                messages.error(request, f"Role '{role}' cannot have system access. Please select a valid role.")
                return redirect("user:edit_system_user", pk=pk)

            # If changing from a role that can't have system access to one that can, show a message
            if employee.role not in ['admin', 'cashier', 'coach'] and role in ['cashier', 'coach']:
                messages.success(request, f"Employee role will be changed from '{employee.role}' to '{role}' to grant system access.")

            # Store the old role for comparison
            old_role = employee.role

            # Update employee fields
            employee.name = name
            employee.role = role
            employee.join_date = join_date

            # Update email if provided
            if email:
                employee.email = email

            # Update is_manager flag based on role
            employee.is_manager = (role == 'admin')

            # Update password if provided
            if password != "":
                employee.password = make_password(password)

            # Save the employee
            employee.save()

            # Update group assignments if the role has changed
            if old_role != role:
                # Clear existing groups
                employee.groups.clear()

                # Add to appropriate group based on new role
                if role == 'admin':
                    admin_group, _ = Group.objects.get_or_create(name='admin')
                    employee.groups.add(admin_group)
                elif role == 'cashier':
                    cashier_group, _ = Group.objects.get_or_create(name='cashier')
                    employee.groups.add(cashier_group)
                elif role == 'coach':
                    coach_group, _ = Group.objects.get_or_create(name='coach')
                    employee.groups.add(coach_group)

                messages.success(request, f"System user '{name}' updated successfully. Role changed from '{old_role}' to '{role}'.")
            else:
                messages.success(request, f"System user '{name}' updated successfully.")

            return redirect("user:register")
        except Exception as e:
            messages.error(request, f"Error updating system user: {str(e)}")

    context = {
        'employee': employee,
    }
    return render(request, "user/edit_system_user.html", context)

# Edit admin user (simplified form for admin users)
@login_required
@module_permission_required(module='user', required_level='edit')
def edit_admin_user(request, pk):
    try:
        employee = User.objects.get(id=pk)
        if employee.role != 'admin':
            messages.error(request, "User is not an admin")
            return redirect("user:admin_users")
    except User.DoesNotExist:
        messages.error(request, "User not found")
        return redirect("user:admin_users")

    # Handle form submission
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            email = request.POST.get("email")
            # We're not using create_day from the form since it's auto_now_add
            # and shouldn't be modified directly

            employee.name = name

            # Update email if provided
            if email:
                employee.email = email

            employee.save()

            # Add a success message
            messages.success(request, f"Admin user '{name}' updated successfully.")
            return redirect("user:admin_users")
        except Exception as e:
            messages.error(request, f"Error updating admin user: {str(e)}")

    context = {
        'employee': employee,
    }
    return render(request, "user/edit_admin_user.html", context)

# User profile view
@login_required
def profile(request):
    user = request.user

    # Update user's last activity timestamp
    user.last_activity = timezone.now()
    user.save(update_fields=['last_activity'])

    # Handle personal information form submission
    if request.method == "POST" and request.POST.get("form_type") == "personal_info":
        try:
            name = request.POST.get("name")
            email = request.POST.get("email")
            phone = request.POST.get("phone")
            dob = request.POST.get("dob") or None
            gender = request.POST.get("gender")

            # Update user fields
            user.name = name
            user.email = email
            user.phone = phone
            user.dob = dob
            user.gender = gender

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                photo = request.FILES['photo']
                if photo:
                    # Save the photo in media/employee_photos directory
                    path = f'employee_photos/{user.id}_{photo.name}'
                    default_storage.save(path, ContentFile(photo.read()))

                    # Store the photo path directly in the user object
                    user.photo = path

            user.save()
            messages.success(request, "Personal information updated successfully.")

        except Exception as e:
            messages.error(request, f"Error updating personal information: {str(e)}")

    # Handle additional information form submission
    elif request.method == "POST" and request.POST.get("form_type") == "additional_info":
        try:
            address = request.POST.get("address")
            schedule = request.POST.get("schedule")
            email_notifications = request.POST.get("email_notifications") == "on"
            system_notifications = request.POST.get("system_notifications") == "on"

            # Combine address and work schedule
            user.address = f"Address: {address}\n\nWork Schedule: {schedule}"
            user.receive_email_notifications = email_notifications
            user.receive_system_notifications = system_notifications

            user.save()
            messages.success(request, "Additional information updated successfully.")

        except Exception as e:
            messages.error(request, f"Error updating additional information: {str(e)}")

    # For demonstration purposes, we'll create some dummy activity data
    # In a real application, you would fetch this from a proper activity log model
    activities = [
        {
            'timestamp': timezone.now() - timezone.timedelta(days=1),
            'action': 'Login',
            'details': 'Logged in from ***********'
        },
        {
            'timestamp': timezone.now() - timezone.timedelta(days=2),
            'action': 'Profile Update',
            'details': 'Updated personal information'
        },
        {
            'timestamp': timezone.now() - timezone.timedelta(days=5),
            'action': 'Password Change',
            'details': 'Changed account password'
        }
    ]

    context = {
        'user': user,
        'activities': activities
    }

    return render(request, 'user/profile.html', context)

# Change password view
@login_required
def change_password(request):
    user = request.user

    if request.method == "POST":
        current_password = request.POST.get("current_password")
        new_password = request.POST.get("new_password")
        confirm_password = request.POST.get("confirm_password")

        # Validate passwords
        if not check_password(current_password, user.password):
            messages.error(request, "Current password is incorrect.")
            return redirect('user:change_password')

        if new_password != confirm_password:
            messages.error(request, "New passwords do not match.")
            return redirect('user:change_password')

        if len(new_password) < 8:
            messages.error(request, "Password must be at least 8 characters long.")
            return redirect('user:change_password')

        # Update password
        user.password = make_password(new_password)
        user.save()

        # Update last activity
        user.last_activity = timezone.now()
        user.save(update_fields=['last_activity'])

        messages.success(request, "Password changed successfully.")
        return redirect('user:profile')

    return render(request, 'user/change_password.html')

# Admin Action Logs view
@login_required
@module_permission_required(module='user', required_level='full')
def admin_action_logs(request):
    """View for displaying admin action logs"""
    # Get all logs, ordered by most recent first
    logs = AdminActionLog.objects.all().order_by('-action_time')

    # Filter by action type if specified
    action_type = request.GET.get('action_type')
    if action_type:
        logs = logs.filter(action_type=action_type)

    # Filter by user if specified
    user_id = request.GET.get('user_id')
    if user_id:
        logs = logs.filter(user_id=user_id)

    # Filter by target user if specified
    target_user_id = request.GET.get('target_user_id')
    if target_user_id:
        logs = logs.filter(target_user_id=target_user_id)

    # Get all admin users for the filter dropdown
    admin_users = User.objects.filter(role='admin')

    context = {
        'logs': logs,
        'admin_users': admin_users,
        'action_types': AdminActionLog.ACTION_TYPES,
        'selected_action_type': action_type,
        'selected_user_id': user_id,
        'selected_target_user_id': target_user_id,
    }

    return render(request, 'user/admin_action_logs_fixed.html', context)


# User Action Logs view - Comprehensive logging for all user actions
@login_required
@module_permission_required(module='user', required_level='full')
def user_action_logs(request):
    """View for displaying comprehensive user action logs across all modules"""
    # Get all logs, ordered by most recent first
    logs = UserActionLog.objects.select_related('user').all().order_by('-action_time')

    # Filter by action type if specified
    action_type = request.GET.get('action_type')
    if action_type:
        logs = logs.filter(action_type=action_type)

    # Filter by module if specified
    module = request.GET.get('module')
    if module:
        logs = logs.filter(module=module)

    # Filter by user if specified
    user_id = request.GET.get('user_id')
    if user_id:
        logs = logs.filter(user_id=user_id)

    # Filter by status if specified
    status = request.GET.get('status')
    if status:
        logs = logs.filter(status=status)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        logs = logs.filter(
            Q(description__icontains=search_query) |
            Q(target_description__icontains=search_query) |
            Q(user__username__icontains=search_query) |
            Q(user__name__icontains=search_query)
        )

    # Date range filtering
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        try:
            date_from_parsed = datetime.strptime(date_from, '%Y-%m-%d')
            logs = logs.filter(action_time__date__gte=date_from_parsed.date())
        except ValueError:
            pass

    if date_to:
        try:
            date_to_parsed = datetime.strptime(date_to, '%Y-%m-%d')
            logs = logs.filter(action_time__date__lte=date_to_parsed.date())
        except ValueError:
            pass

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(logs, 50)  # Show 50 logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get all users for filter dropdown
    all_users = User.objects.filter(is_active=True).order_by('username')

    context = {
        'page_obj': page_obj,
        'logs': page_obj,  # For template compatibility
        'all_users': all_users,
        'action_types': UserActionLog.ACTION_TYPES,
        'modules': UserActionLog.MODULE_CHOICES,
        'status_choices': UserActionLog.STATUS_CHOICES,
        'selected_action_type': action_type,
        'selected_module': module,
        'selected_user_id': user_id,
        'selected_status': status,
        'search_query': search_query,
        'date_from': date_from,
        'date_to': date_to,
        'total_logs': logs.count() if not hasattr(logs, 'count') else paginator.count,
    }

    return render(request, 'user/user_action_logs.html', context)


# Removed features as per requirements
# - Bulk User Import/Export
# - Custom Role Management
