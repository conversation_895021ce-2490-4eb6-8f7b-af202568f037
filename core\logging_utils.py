"""
Utility functions for user action logging across the application
"""
from user.models import UserActionLog


def get_client_ip(request):
    """
    Extract the client IP address from the request
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def log_user_action(request, action_type, module, status='success', target_model='',
                   target_id='', target_description='', before_values=None,
                   after_values=None, description='', additional_data=None,
                   financial_impact=None):
    """
    Convenience function to log user actions with automatic IP and user agent extraction

    Args:
        request: Django request object
        action_type: Type of action performed (from UserActionLog.ACTION_TYPES)
        module: Module where action was performed (from UserActionLog.MODULE_CHOICES)
        status: Status of the action ('success', 'failed', 'partial')
        target_model: Model name that was affected
        target_id: ID of the affected record
        target_description: Human-readable description of the target
        before_values: JSON data of values before the action
        after_values: JSON data of values after the action
        description: Detailed description of the action
        additional_data: Any additional data to store
        financial_impact: Financial impact in KHR (positive for income, negative for expense)

    Returns:
        UserActionLog instance
    """
    # Only log if user is authenticated, or if it's an authentication action
    user = None
    if request.user.is_authenticated:
        user = request.user
    elif action_type in ['login', 'failed_login', 'logout']:
        # For auth actions, we allow logging even without authenticated user
        user = None
    else:
        # For non-auth actions, we need an authenticated user
        if not request.user.is_authenticated:
            print(f"Skipping log for unauthenticated user: {action_type}")
            return None

    return UserActionLog.log_action(
        user=user,
        action_type=action_type,
        module=module,
        status=status,
        target_model=target_model,
        target_id=target_id,
        target_description=target_description,
        before_values=before_values,
        after_values=after_values,
        ip_address=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        description=description,
        additional_data=additional_data,
        financial_impact=financial_impact
    )


def log_delete_action(request, module, target_model, target_id, target_description,
                     financial_impact=None, additional_data=None, status='success'):
    """
    Convenience function specifically for delete actions
    """
    action_type = f'delete_{target_model.lower()}'
    description = f'Deleted {target_model} {target_id}: {target_description}'

    return log_user_action(
        request=request,
        action_type=action_type,
        module=module,
        status=status,
        target_model=target_model,
        target_id=target_id,
        target_description=target_description,
        description=description,
        additional_data=additional_data,
        financial_impact=financial_impact
    )


def log_edit_action(request, module, target_model, target_id, target_description,
                   before_values=None, after_values=None, additional_data=None,
                   status='success'):
    """
    Convenience function specifically for edit actions
    """
    action_type = f'edit_{target_model.lower()}'
    description = f'Edited {target_model} {target_id}: {target_description}'

    return log_user_action(
        request=request,
        action_type=action_type,
        module=module,
        status=status,
        target_model=target_model,
        target_id=target_id,
        target_description=target_description,
        before_values=before_values,
        after_values=after_values,
        description=description,
        additional_data=additional_data
    )


def log_create_action(request, module, target_model, target_id, target_description,
                     after_values=None, additional_data=None, status='success'):
    """
    Convenience function specifically for create actions
    """
    action_type = f'create_{target_model.lower()}'
    description = f'Created {target_model} {target_id}: {target_description}'

    return log_user_action(
        request=request,
        action_type=action_type,
        module=module,
        status=status,
        target_model=target_model,
        target_id=target_id,
        target_description=target_description,
        after_values=after_values,
        description=description,
        additional_data=additional_data
    )


def log_auth_action(request, action_type, status='success', description='',
                   additional_data=None):
    """
    Convenience function for authentication-related actions
    """
    return log_user_action(
        request=request,
        action_type=action_type,
        module='auth',
        status=status,
        description=description,
        additional_data=additional_data
    )


def log_settings_action(request, action_type, target_description='',
                       before_values=None, after_values=None, status='success'):
    """
    Convenience function for settings changes
    """
    description = f'Settings change: {target_description}'

    return log_user_action(
        request=request,
        action_type=action_type,
        module='settings',
        status=status,
        target_model='Settings',
        target_description=target_description,
        before_values=before_values,
        after_values=after_values,
        description=description
    )
